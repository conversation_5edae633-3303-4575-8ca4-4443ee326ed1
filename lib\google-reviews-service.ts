import { supabase } from "./supabase";

// Types for Google Reviews
export interface GoogleReview {
	id: string;
	author_name: string;
	author_url?: string;
	language: string;
	profile_photo_url?: string;
	rating: number;
	relative_time_description: string;
	text: string;
	time: number;
	translated?: boolean;
}

export interface GooglePlacesResponse {
	result: {
		reviews?: GoogleReview[];
		rating?: number;
		user_ratings_total?: number;
	};
	status: string;
	error_message?: string;
}

export interface CachedReview {
	id: string;
	author_name: string;
	author_initials: string;
	rating: number;
	text: string;
	date: string;
	relative_time: string;
	profile_photo_url?: string;
	source: "google_places" | "google_business" | "manual";
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

export interface ReviewsServiceConfig {
	googlePlacesApiKey?: string;
	googlePlaceId?: string;
	fallbackToCache: boolean;
	maxCacheAge: number; // in hours
	minRating?: number;
	maxReviews?: number;
}

class GoogleReviewsService {
	private config: ReviewsServiceConfig;

	constructor(config: ReviewsServiceConfig) {
		this.config = {
			fallbackToCache: true,
			maxCacheAge: 24, // 24 hours default
			maxReviews: 10,
			...config,
		};
	}

	/**
	 * Fetch reviews from Google Places API
	 */
	async fetchFromGooglePlaces(): Promise<GoogleReview[]> {
		if (!this.config.googlePlacesApiKey || !this.config.googlePlaceId) {
			throw new Error("Google Places API key and Place ID are required");
		}

		const url = new URL("https://maps.googleapis.com/maps/api/place/details/json");
		url.searchParams.set("place_id", this.config.googlePlaceId);
		url.searchParams.set("fields", "reviews,rating,user_ratings_total");
		url.searchParams.set("key", this.config.googlePlacesApiKey);

		try {
			const response = await fetch(url.toString());
			const data: GooglePlacesResponse = await response.json();

			if (data.status !== "OK") {
				throw new Error(`Google Places API error: ${data.status} - ${data.error_message || "Unknown error"}`);
			}

			return data.result.reviews || [];
		} catch (error) {
			console.error("Error fetching from Google Places API:", error);
			throw error;
		}
	}

	/**
	 * Transform Google review to our cached format
	 */
	private transformGoogleReview(review: GoogleReview): Omit<CachedReview, "id" | "created_at" | "updated_at"> {
		// Generate initials from author name
		const initials = review.author_name
			.split(" ")
			.map((name) => name.charAt(0).toUpperCase())
			.slice(0, 2)
			.join("");

		return {
			author_name: review.author_name,
			author_initials: initials,
			rating: review.rating,
			text: review.text || "",
			date: new Date(review.time * 1000).toISOString(),
			relative_time: review.relative_time_description,
			profile_photo_url: review.profile_photo_url,
			source: "google_places",
			is_active: true,
		};
	}

	/**
	 * Cache reviews in database
	 */
	async cacheReviews(reviews: GoogleReview[]): Promise<void> {
		if (!reviews.length) return;

		const transformedReviews = reviews.map((review) => ({
			...this.transformGoogleReview(review),
			external_id: `google_${review.time}_${review.author_name.replace(/\s+/g, "_")}`,
		}));

		try {
			// Use upsert to avoid duplicates
			const { error } = await supabase.from("cached_reviews").upsert(transformedReviews, {
				onConflict: "external_id",
				ignoreDuplicates: false,
			});

			if (error) {
				console.error("Error caching reviews:", error);
				throw error;
			}

			console.log(`Successfully cached ${transformedReviews.length} reviews`);
		} catch (error) {
			console.error("Error caching reviews:", error);
			throw error;
		}
	}

	/**
	 * Get cached reviews from database
	 */
	async getCachedReviews(): Promise<CachedReview[]> {
		try {
			let query = supabase
				.from("cached_reviews")
				.select("*")
				.eq("is_active", true)
				.order("date", { ascending: false });

			// Apply rating filter if specified
			if (this.config.minRating) {
				query = query.gte("rating", this.config.minRating);
			}

			// Apply limit if specified
			if (this.config.maxReviews) {
				query = query.limit(this.config.maxReviews);
			}

			const { data, error } = await query;

			if (error) {
				console.error("Error fetching cached reviews:", error);
				throw error;
			}

			return data || [];
		} catch (error) {
			console.error("Error getting cached reviews:", error);
			throw error;
		}
	}

	/**
	 * Check if cached reviews are still fresh
	 */
	async isCacheFresh(): Promise<boolean> {
		try {
			const { data, error } = await supabase
				.from("cached_reviews")
				.select("updated_at")
				.order("updated_at", { ascending: false })
				.limit(1);

			if (error || !data || data.length === 0) {
				return false;
			}

			const lastUpdate = new Date(data[0].updated_at);
			const now = new Date();
			const hoursSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);

			return hoursSinceUpdate < this.config.maxCacheAge;
		} catch (error) {
			console.error("Error checking cache freshness:", error);
			return false;
		}
	}

	/**
	 * Get reviews with fallback strategy
	 */
	async getReviews(): Promise<CachedReview[]> {
		try {
			// First, try to get fresh reviews from Google Places API
			if (this.config.googlePlacesApiKey && this.config.googlePlaceId) {
				try {
					const googleReviews = await this.fetchFromGooglePlaces();

					if (googleReviews.length > 0) {
						// Cache the fresh reviews
						await this.cacheReviews(googleReviews);

						// Return the cached reviews (which now include the fresh ones)
						return await this.getCachedReviews();
					}
				} catch (error) {
					console.warn("Failed to fetch from Google Places API, falling back to cache:", error);
				}
			}

			// Fallback to cached reviews
			if (this.config.fallbackToCache) {
				const cachedReviews = await this.getCachedReviews();

				if (cachedReviews.length > 0) {
					console.log("Using cached reviews as fallback");
					return cachedReviews;
				}
			}

			// If no cached reviews available, return empty array
			console.warn("No reviews available from any source");
			return [];
		} catch (error) {
			console.error("Error in getReviews:", error);

			// Last resort: try to get any cached reviews
			if (this.config.fallbackToCache) {
				try {
					return await this.getCachedReviews();
				} catch (cacheError) {
					console.error("Even cached reviews failed:", cacheError);
				}
			}

			return [];
		}
	}

	/**
	 * Refresh reviews manually
	 */
	async refreshReviews(): Promise<{ success: boolean; count: number; error?: string }> {
		try {
			if (!this.config.googlePlacesApiKey || !this.config.googlePlaceId) {
				throw new Error("Google Places API configuration missing");
			}

			const googleReviews = await this.fetchFromGooglePlaces();
			await this.cacheReviews(googleReviews);

			return {
				success: true,
				count: googleReviews.length,
			};
		} catch (error) {
			console.error("Error refreshing reviews:", error);
			return {
				success: false,
				count: 0,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}
}

// Export singleton instance with safe configuration
export const googleReviewsService = new GoogleReviewsService({
	googlePlacesApiKey: process.env.GOOGLE_PLACES_API_KEY || undefined,
	googlePlaceId: process.env.GOOGLE_BUSINESS_PLACE_ID || undefined,
	fallbackToCache: true,
	maxCacheAge: 24,
	minRating: 4, // Only show 4+ star reviews
	maxReviews: 10,
});

export default GoogleReviewsService;
