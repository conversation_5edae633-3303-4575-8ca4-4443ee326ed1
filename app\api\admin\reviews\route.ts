import { googleReviewsService } from "@/lib/google-reviews-service";
import { supabaseAdmin } from "@/lib/supabase";
import { withAdminAuth } from "@/lib/auth-middleware";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/admin/reviews - Get all cached reviews for admin management
export const GET = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const source = searchParams.get("source");
    const isActive = searchParams.get("isActive");
    const minRating = searchParams.get("minRating");

    const offset = (page - 1) * limit;

    if (!supabaseAdmin) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
    }

    let query = supabaseAdmin
      .from("cached_reviews")
      .select("*", { count: "exact" })
      .order("date", { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (source) {
      query = query.eq("source", source);
    }
    if (isActive !== null) {
      query = query.eq("is_active", isActive === "true");
    }
    if (minRating) {
      query = query.gte("rating", parseInt(minRating));
    }

    const { data: reviews, error, count } = await query;

    if (error) {
      console.error("Error fetching reviews:", error);
      return NextResponse.json({ error: "Failed to fetch reviews" }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      reviews: reviews || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    });
  } catch (error) {
    console.error("Error in admin reviews GET:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// POST /api/admin/reviews/refresh - Manually refresh reviews from Google API
export const POST = withAdminAuth(async (request: NextRequest, user) => {
  try {
    console.log(`Admin ${user.email} requested manual review refresh`);
    
    const refreshResult = await googleReviewsService.refreshReviews();
    
    if (refreshResult.success) {
      // Log the action
      if (supabaseAdmin) {
        await supabaseAdmin.from("audit_log").insert({
          user_id: user.id,
          action: "refresh_reviews",
          resource_type: "reviews",
          details: {
            count: refreshResult.count,
            timestamp: new Date().toISOString(),
          },
        });
      }

      return NextResponse.json({
        success: true,
        message: `Successfully refreshed ${refreshResult.count} reviews`,
        count: refreshResult.count,
        timestamp: new Date().toISOString(),
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: "Failed to refresh reviews",
          details: refreshResult.error,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in manual refresh:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to refresh reviews",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
});

// PATCH /api/admin/reviews - Bulk update review visibility
export const PATCH = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { reviewIds, isActive }: { reviewIds: string[]; isActive: boolean } = await request.json();

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return NextResponse.json(
        { error: "reviewIds array is required" },
        { status: 400 }
      );
    }

    if (!supabaseAdmin) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
    }

    const { data, error } = await supabaseAdmin
      .from("cached_reviews")
      .update({ 
        is_active: isActive,
        updated_at: new Date().toISOString(),
      })
      .in("id", reviewIds)
      .select();

    if (error) {
      console.error("Error updating reviews:", error);
      return NextResponse.json({ error: "Failed to update reviews" }, { status: 500 });
    }

    // Log the action
    await supabaseAdmin.from("audit_log").insert({
      user_id: user.id,
      action: isActive ? "activate_reviews" : "deactivate_reviews",
      resource_type: "reviews",
      details: {
        reviewIds,
        count: data?.length || 0,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully ${isActive ? 'activated' : 'deactivated'} ${data?.length || 0} reviews`,
      updatedCount: data?.length || 0,
    });
  } catch (error) {
    console.error("Error in bulk update:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// DELETE /api/admin/reviews - Delete reviews
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { reviewIds }: { reviewIds: string[] } = await request.json();

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return NextResponse.json(
        { error: "reviewIds array is required" },
        { status: 400 }
      );
    }

    if (!supabaseAdmin) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 500 });
    }

    const { data, error } = await supabaseAdmin
      .from("cached_reviews")
      .delete()
      .in("id", reviewIds)
      .select();

    if (error) {
      console.error("Error deleting reviews:", error);
      return NextResponse.json({ error: "Failed to delete reviews" }, { status: 500 });
    }

    // Log the action
    await supabaseAdmin.from("audit_log").insert({
      user_id: user.id,
      action: "delete_reviews",
      resource_type: "reviews",
      details: {
        reviewIds,
        count: data?.length || 0,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${data?.length || 0} reviews`,
      deletedCount: data?.length || 0,
    });
  } catch (error) {
    console.error("Error in delete:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});
