# Google Reviews Integration Setup Guide

This guide explains how to set up and configure the Google Reviews integration for the Soleil et Découverte website.

## Overview

The Google Reviews integration provides:
- **Real Google Reviews**: Fetches authentic customer reviews from Google
- **Fallback System**: Shows cached reviews or default testimonials when API is unavailable
- **Admin Management**: Interface to manage review display and refresh data
- **Compliance**: Follows Google's API terms of service and display policies

## Setup Options

### Option 1: Google Places API (Limited but Immediate)
- ✅ No approval needed
- ✅ Immediate setup
- ❌ Limited to 5 reviews only
- ❌ Basic review data

### Option 2: Google Business Profile API (Recommended for Production)
- ✅ Unlimited reviews
- ✅ Full review data and management
- ❌ Requires 2-4 week approval process
- ❌ Business verification needed

### Option 3: Hybrid Approach (Current Implementation)
- ✅ Start with Google Places API immediately
- ✅ Fallback to cached/default testimonials
- ✅ Prepare for Google Business Profile API approval
- ✅ Seamless transition when approved

## Configuration Steps

### 1. Google Cloud Setup

#### For Google Places API:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the "Places API"
4. Create credentials (API Key)
5. Restrict the API key to your domain

#### For Google Business Profile API (Future):
1. Follow the same steps as above
2. Enable "Google My Business API"
3. Apply for API access at [Google Business Profile API](https://developers.google.com/my-business/content/prereqs#request-access)
4. Set up OAuth 2.0 credentials

### 2. Find Your Google Place ID

1. Go to [Google Place ID Finder](https://developers.google.com/maps/documentation/places/web-service/place-id)
2. Search for "Soleil et Découverte" or your business address
3. Copy the Place ID (starts with "ChIJ...")

### 3. Environment Variables

Add these to your `.env.local` file:

```env
# Google Places API (Current)
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
GOOGLE_BUSINESS_PLACE_ID=your_google_place_id_here

# Google Business Profile API (Future)
GOOGLE_BUSINESS_PROFILE_CLIENT_ID=your_client_id_here
GOOGLE_BUSINESS_PROFILE_CLIENT_SECRET=your_client_secret_here
```

### 4. Database Setup

The database schema is already created with these tables:
- `cached_reviews`: Stores Google reviews locally
- Includes proper indexing and RLS policies

## Usage

### Frontend Integration

The homepage now uses the `GoogleReviews` component:

```tsx
<GoogleReviews 
  maxReviews={10}
  minRating={4}
  autoRefresh={false}
  showSource={false}
  enableFallback={true}
  className="max-w-6xl mx-auto"
/>
```

### API Endpoints

- `GET /api/reviews` - Fetch reviews for public display
- `GET /api/admin/reviews` - Admin: List all cached reviews
- `POST /api/admin/reviews` - Admin: Refresh from Google API
- `PATCH /api/admin/reviews` - Admin: Bulk update review visibility
- `DELETE /api/admin/reviews` - Admin: Delete reviews

### Admin Interface

Access the reviews management at `/admin` (requires admin authentication):
- View all cached reviews
- Manually refresh from Google API
- Toggle review visibility
- Filter and search reviews
- Bulk operations

## Testing

### 1. Test Fallback System
Without API keys configured, the system should show default testimonials.

### 2. Test with Sample Data
The database includes sample reviews for testing the display.

### 3. Test API Integration
Once API keys are configured:
1. Visit the homepage to see reviews
2. Check admin interface for management
3. Test manual refresh functionality

## Compliance with Google Policies

### Display Requirements
- ✅ Shows authentic review data
- ✅ Includes author names (or initials for privacy)
- ✅ Displays star ratings accurately
- ✅ Shows review dates
- ✅ Maintains review text integrity

### API Usage
- ✅ Respects rate limits
- ✅ Caches data to reduce API calls
- ✅ Handles errors gracefully
- ✅ Provides fallback content

### Privacy
- ✅ Can show initials instead of full names
- ✅ Respects user profile photo settings
- ✅ Allows review moderation

## Troubleshooting

### Common Issues

1. **No reviews showing**
   - Check API key configuration
   - Verify Place ID is correct
   - Check browser console for errors

2. **API quota exceeded**
   - Reviews are cached to minimize API calls
   - Consider upgrading Google Cloud plan

3. **Reviews not updating**
   - Use admin interface to manually refresh
   - Check API key permissions

### Error Handling

The system includes comprehensive error handling:
- Network failures → Fallback to cached reviews
- API errors → Show fallback testimonials
- Invalid configuration → Display error messages

## Migration Path

### Current State (Phase 1)
- Google Places API integration (5 reviews max)
- Fallback testimonials system
- Admin management interface

### Future Enhancement (Phase 2)
- Apply for Google Business Profile API access
- Upgrade to unlimited reviews
- Enhanced review management features

### Transition
The system is designed for seamless transition:
1. Apply for Google Business Profile API access
2. Update environment variables when approved
3. System automatically uses new API
4. No code changes required

## Support

For technical support or questions about the Google Reviews integration:
1. Check the troubleshooting section above
2. Review Google's API documentation
3. Contact the development team

## Security Notes

- API keys should never be exposed to client-side code
- Use environment variables for all credentials
- Regularly rotate API keys
- Monitor API usage in Google Cloud Console
